using System;
using System.Text;
using UnityEngine;

namespace Storage
{
    /// <summary>
    /// 存储系统的配置类，管理所有存储相关的设置
    /// </summary>
    [Serializable]
    public class StorageSettings : ICloneable
    {
        #region 枚举定义

        /// <summary>
        /// 加密类型
        /// </summary>
        public enum EncryptionType
        {
            None,   // 无加密
            AES,    // AES加密
            XOR     // 异或加密
        }

        /// <summary>
        /// 存储位置
        /// </summary>
        public enum StorageLocation
        {
            File,           // 文件存储
            PlayerPrefs,    // PlayerPrefs存储
            Memory          // 仅内存存储
        }

        /// <summary>
        /// 文件目录类型
        /// </summary>
        public enum DirectoryType
        {
            PersistentDataPath,  // 持久化数据路径
            DataPath,            // 数据路径
            StreamingAssetsPath  // StreamingAssets路径
        }

        #endregion

        #region 默认设置

        private static StorageSettings _defaultSettings;

        /// <summary>
        /// 默认设置实例
        /// </summary>
        public static StorageSettings DefaultSettings
        {
            get
            {
                if (_defaultSettings == null)
                {
                    _defaultSettings = new StorageSettings();
                }
                return _defaultSettings;
            }
        }

        #endregion

        #region 配置字段

        [SerializeField]
        private string _filePath = "SaveData.json";
        /// <summary>文件路径</summary>
        public string FilePath
        {
            get { return _filePath; }
            set { _filePath = value; }
        }

        [SerializeField]
        private StorageLocation _location = StorageLocation.File;
        /// <summary>存储位置</summary>
        public StorageLocation Location
        {
            get
            {
                // WebGL平台不支持文件存储，自动切换到PlayerPrefs
                if (_location == StorageLocation.File && Application.platform == RuntimePlatform.WebGLPlayer)
                    return StorageLocation.PlayerPrefs;
                return _location;
            }
            set { _location = value; }
        }

        [SerializeField]
        private DirectoryType _directory = DirectoryType.PersistentDataPath;
        /// <summary>文件目录类型</summary>
        public DirectoryType Directory
        {
            get { return _directory; }
            set { _directory = value; }
        }

        [SerializeField]
        private EncryptionType _encryptionType = EncryptionType.None;
        /// <summary>加密类型</summary>
        public EncryptionType Encryption
        {
            get { return _encryptionType; }
            set { _encryptionType = value; }
        }

        [SerializeField]
        private string _encryptionPassword = "DefaultPassword";
        /// <summary>加密密码</summary>
        public string EncryptionPassword
        {
            get { return _encryptionPassword; }
            set { _encryptionPassword = value; }
        }

        [SerializeField]
        private bool _prettyPrint = true;
        /// <summary>是否格式化JSON输出</summary>
        public bool PrettyPrint
        {
            get { return _prettyPrint; }
            set { _prettyPrint = value; }
        }

        [SerializeField]
        private bool _autoSaveOnApplicationPause = true;
        /// <summary>应用暂停时自动保存</summary>
        public bool AutoSaveOnApplicationPause
        {
            get { return _autoSaveOnApplicationPause; }
            set { _autoSaveOnApplicationPause = value; }
        }

        [SerializeField]
        private bool _autoSaveOnApplicationQuit = true;
        /// <summary>应用退出时自动保存</summary>
        public bool AutoSaveOnApplicationQuit
        {
            get { return _autoSaveOnApplicationQuit; }
            set { _autoSaveOnApplicationQuit = value; }
        }

        [SerializeField]
        private int _bufferSize = 4096;
        /// <summary>缓冲区大小</summary>
        public int BufferSize
        {
            get { return _bufferSize; }
            set { _bufferSize = Mathf.Max(1024, value); }
        }

        [SerializeField]
        private Encoding _encoding = Encoding.UTF8;
        /// <summary>文本编码</summary>
        public Encoding TextEncoding
        {
            get { return _encoding ?? Encoding.UTF8; }
            set { _encoding = value ?? Encoding.UTF8; }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public StorageSettings()
        {
            // 使用默认值
        }

        /// <summary>
        /// 带文件路径的构造函数
        /// </summary>
        /// <param name="filePath">文件路径</param>
        public StorageSettings(string filePath) : this()
        {
            _filePath = filePath;
        }

        /// <summary>
        /// 带文件路径和存储位置的构造函数
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="location">存储位置</param>
        public StorageSettings(string filePath, StorageLocation location) : this(filePath)
        {
            _location = location;
        }

        /// <summary>
        /// 带加密设置的构造函数
        /// </summary>
        /// <param name="encryptionType">加密类型</param>
        /// <param name="encryptionPassword">加密密码</param>
        public StorageSettings(EncryptionType encryptionType, string encryptionPassword) : this()
        {
            _encryptionType = encryptionType;
            _encryptionPassword = encryptionPassword;
        }

        /// <summary>
        /// 复制构造函数
        /// </summary>
        /// <param name="other">要复制的设置</param>
        public StorageSettings(StorageSettings other) : this()
        {
            if (other != null)
            {
                CopyFrom(other);
            }
        }

        #endregion

        #region 公共方法

        /// <summary>
        /// 获取完整的文件路径
        /// </summary>
        /// <returns>完整的文件路径</returns>
        public string GetFullPath()
        {
            if (string.IsNullOrEmpty(_filePath))
                throw new ArgumentException("File path cannot be null or empty");

            // 如果是绝对路径，直接返回
            if (IsAbsolutePath(_filePath))
                return _filePath;

            // 根据目录类型构建完整路径
            string basePath;
            switch (_directory)
            {
                case DirectoryType.PersistentDataPath:
                    basePath = Application.persistentDataPath;
                    break;
                case DirectoryType.DataPath:
                    basePath = Application.dataPath;
                    break;
                case DirectoryType.StreamingAssetsPath:
                    basePath = Application.streamingAssetsPath;
                    break;
                default:
                    throw new NotImplementedException($"Directory type {_directory} not implemented");
            }

            return System.IO.Path.Combine(basePath, _filePath);
        }

        /// <summary>
        /// 获取备份文件的完整路径
        /// </summary>
        /// <returns>备份文件的完整路径</returns>
        public string GetBackupPath()
        {
            return GetFullPath() + ".bak";
        }

        /// <summary>
        /// 克隆设置对象
        /// </summary>
        /// <returns>克隆的设置对象</returns>
        public object Clone()
        {
            return new StorageSettings(this);
        }

        /// <summary>
        /// 从另一个设置对象复制配置
        /// </summary>
        /// <param name="other">源设置对象</param>
        public void CopyFrom(StorageSettings other)
        {
            if (other == null) return;

            _filePath = other._filePath;
            _location = other._location;
            _directory = other._directory;
            _encryptionType = other._encryptionType;
            _encryptionPassword = other._encryptionPassword;
            _prettyPrint = other._prettyPrint;
            _autoSaveOnApplicationPause = other._autoSaveOnApplicationPause;
            _autoSaveOnApplicationQuit = other._autoSaveOnApplicationQuit;
            _bufferSize = other._bufferSize;
            _encoding = other._encoding;
        }

        #endregion

        #region 私有方法

        /// <summary>
        /// 检查路径是否为绝对路径
        /// </summary>
        /// <param name="path">路径</param>
        /// <returns>是否为绝对路径</returns>
        private static bool IsAbsolutePath(string path)
        {
            if (string.IsNullOrEmpty(path)) return false;

            // Windows绝对路径检查
            if (path.Length > 1 && path[1] == ':') return true;

            // Unix/Linux绝对路径检查
            if (path.Length > 0 && (path[0] == '/' || path[0] == '\\')) return true;

            return false;
        }

        #endregion
    }
}
