using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using UnityEngine;
using Storage.Serialization;
using Storage.Encryption;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 高性能数据持久化模块的主要API类
    /// 提供基于key-value的数据增删改查操作，支持内存缓存和文件持久化
    /// </summary>
    public static class Storage
    {
        #region 私有字段

        private static StorageCache _cache;
        private static StorageSettings _defaultSettings;
        private static bool _isInitialized = false;

        #endregion

        #region 属性

        /// <summary>
        /// 默认设置
        /// </summary>
        public static StorageSettings DefaultSettings
        {
            get
            {
                if (_defaultSettings == null)
                {
                    _defaultSettings = new StorageSettings();
                }
                return _defaultSettings;
            }
            set
            {
                _defaultSettings = value;
            }
        }

        /// <summary>
        /// 内存缓存
        /// </summary>
        private static StorageCache Cache
        {
            get
            {
                if (_cache == null)
                {
                    _cache = new StorageCache();
                }
                return _cache;
            }
        }

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public static bool IsInitialized
        {
            get { return _isInitialized; }
        }

        #endregion

        #region 初始化方法

        /// <summary>
        /// 初始化存储系统
        /// </summary>
        /// <param name="settings">设置对象</param>
        public static void Initialize(StorageSettings settings = null)
        {
            if (_isInitialized)
            {
                NLogger.LogWarning("Storage system is already initialized");
                return;
            }

            _defaultSettings = settings ?? new StorageSettings();
            _cache = new StorageCache();

            // 注册应用程序事件
            RegisterApplicationEvents();

            _isInitialized = true;
            NLogger.Log("Storage system initialized successfully");
        }

        /// <summary>
        /// 注册应用程序事件
        /// </summary>
        private static void RegisterApplicationEvents()
        {
            Application.quitting += OnApplicationQuitting;
            Application.focusChanged += OnApplicationFocusChanged;
        }

        #endregion

        #region 数据操作方法

        /// <summary>
        /// 保存数据到内存缓存
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        public static void Set<T>(string key, T value)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return;
            }

            Cache.Set(key, value);
            NLogger.Log<string, string>("Key: {0}, Type: {1}", key, typeof(T).Name);
        }

        /// <summary>
        /// 从内存缓存获取数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <returns>数据值</returns>
        public static T Get<T>(string key)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return default;
            }

            return Cache.Get<T>(key);
        }

        /// <summary>
        /// 从内存缓存获取数据，如果不存在则返回默认值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>数据值或默认值</returns>
        public static T Get<T>(string key, T defaultValue)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return defaultValue;
            }

            return Cache.Get(key, defaultValue);
        }

        /// <summary>
        /// 检查键是否存在
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否存在</returns>
        public static bool ContainsKey(string key)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
                return false;

            return Cache.ContainsKey(key);
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否删除成功</returns>
        public static bool Remove(string key)
        {
            EnsureInitialized();

            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return false;
            }

            return Cache.Remove(key);
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public static void Clear()
        {
            EnsureInitialized();
            Cache.Clear();
            NLogger.Log("All data cleared from cache");
        }

        /// <summary>
        /// 获取所有键
        /// </summary>
        /// <returns>键的集合</returns>
        public static ICollection<string> GetAllKeys()
        {
            EnsureInitialized();
            return Cache.Keys;
        }

        #endregion

        #region 文件操作方法

        /// <summary>
        /// 保存所有数据到文件
        /// </summary>
        /// <param name="settings">设置对象</param>
        public static void SaveToFile(StorageSettings settings = null)
        {
            _ = SaveToFileAsync(settings);
        }

        /// <summary>
        /// 异步保存所有数据到文件
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>异步任务</returns>
        public static async Task SaveToFileAsync(StorageSettings settings = null)
        {
            EnsureInitialized();

            var actualSettings = settings ?? DefaultSettings;

            try
            {
                var data = Cache.GetAllData();

                // 异步序列化
                string json = await Task.Run(() => StorageSerializer.SerializeToJson(data, actualSettings.PrettyPrint));

                // 加密处理
                if (actualSettings.Encryption != StorageSettings.EncryptionType.None)
                {
                    json = await Task.Run(() => EncryptData(json, actualSettings));
                }

                // 异步保存到文件
                await WriteToFileAsync(json, actualSettings);

                Cache.MarkAsSaved();
                NLogger.Log<int, string>("Saved {0} items to file: {1}", data.Count, actualSettings.GetFullPath());
            }
            catch (Exception ex)
            {
                NLogger.LogError<string>("Failed to save data: {0}", ex.Message);
            }
        }

        /// <summary>
        /// 保存指定键的数据到文件
        /// </summary>
        /// <param name="keys">要保存的键列表</param>
        /// <param name="settings">设置对象</param>
        public static void SaveKeysToFile(IEnumerable<string> keys, StorageSettings settings = null)
        {
            _ = SaveKeysToFileAsync(keys, settings);
        }

        /// <summary>
        /// 异步保存指定键的数据到文件
        /// </summary>
        /// <param name="keys">要保存的键列表</param>
        /// <param name="settings">设置对象</param>
        /// <returns>异步任务</returns>
        public static async Task SaveKeysToFileAsync(IEnumerable<string> keys, StorageSettings settings = null)
        {
            EnsureInitialized();

            if (keys == null)
            {
                NLogger.LogError("Keys collection cannot be null");
                return;
            }

            var actualSettings = settings ?? DefaultSettings;
            var dataToSave = new Dictionary<string, object>();

            foreach (var key in keys)
            {
                if (Cache.ContainsKey(key))
                {
                    dataToSave[key] = Cache.Get<object>(key);
                }
            }

            if (dataToSave.Count == 0)
            {
                NLogger.LogWarning("No valid keys found to save");
                return;
            }

            try
            {
                // 异步序列化
                string json = await Task.Run(() => StorageSerializer.SerializeToJson(dataToSave, actualSettings.PrettyPrint));

                // 加密处理
                if (actualSettings.Encryption != StorageSettings.EncryptionType.None)
                {
                    json = await Task.Run(() => EncryptData(json, actualSettings));
                }

                // 异步保存到文件
                await WriteToFileAsync(json, actualSettings);

                NLogger.Log<int, string>("Saved {0} items to file: {1}", dataToSave.Count, actualSettings.GetFullPath());
            }
            catch (Exception ex)
            {
                NLogger.LogError<string>("Failed to save data: {0}", ex.Message);
            }
        }

        /// <summary>
        /// 从文件加载数据
        /// </summary>
        /// <param name="settings">设置对象</param>
        public static void LoadFromFile(StorageSettings settings = null)
        {
            _ = LoadFromFileAsync(settings);
        }

        /// <summary>
        /// 异步从文件加载数据
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>异步任务</returns>
        public static async Task LoadFromFileAsync(StorageSettings settings = null)
        {
            EnsureInitialized();

            var actualSettings = settings ?? DefaultSettings;

            try
            {
                // 异步读取文件
                string json = await ReadFromFileAsync(actualSettings);

                if (string.IsNullOrEmpty(json))
                {
                    NLogger.LogWarning<string>("File is empty or does not exist: {0}", actualSettings.GetFullPath());
                    return;
                }

                // 解密处理
                if (actualSettings.Encryption != StorageSettings.EncryptionType.None)
                {
                    json = await Task.Run(() => DecryptData(json, actualSettings));
                }

                // 异步反序列化
                var data = await Task.Run(() => StorageSerializer.DeserializeFromJson(json));
                Cache.LoadFromDictionary(data);

                NLogger.Log<int, string>("Loaded {0} items from file: {1}", data.Count, actualSettings.GetFullPath());
            }
            catch (Exception ex)
            {
                NLogger.LogError<string>("Failed to load data: {0}", ex.Message);
            }
        }

        /// <summary>
        /// 检查文件是否存在
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>文件是否存在</returns>
        public static bool FileExists(StorageSettings settings = null)
        {
            var actualSettings = settings ?? DefaultSettings;

            switch (actualSettings.Location)
            {
                case StorageSettings.StorageLocation.File:
                    return File.Exists(actualSettings.GetFullPath());
                case StorageSettings.StorageLocation.PlayerPrefs:
                    return PlayerPrefs.HasKey(actualSettings.FilePath);
                default:
                    return false;
            }
        }

        /// <summary>
        /// 删除文件
        /// </summary>
        /// <param name="settings">设置对象</param>
        public static void DeleteFile(StorageSettings settings = null)
        {
            var actualSettings = settings ?? DefaultSettings;

            try
            {
                switch (actualSettings.Location)
                {
                    case StorageSettings.StorageLocation.File:
                        if (File.Exists(actualSettings.GetFullPath()))
                        {
                            File.Delete(actualSettings.GetFullPath());
                            NLogger.Log<string>("File deleted: {0}", actualSettings.GetFullPath());
                        }
                        // 同时删除可能存在的备份文件
                        if (File.Exists(actualSettings.GetBackupPath()))
                        {
                            File.Delete(actualSettings.GetBackupPath());
                            NLogger.Log<string>("Backup file deleted: {0}", actualSettings.GetBackupPath());
                        }
                        break;
                    case StorageSettings.StorageLocation.PlayerPrefs:
                        PlayerPrefs.DeleteKey(actualSettings.FilePath);
                        NLogger.Log<string>("PlayerPrefs key deleted: {0}", actualSettings.FilePath);
                        break;
                }
            }
            catch (Exception ex)
            {
                NLogger.LogError<string>("Failed to delete file: {0}", ex.Message);
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 确保系统已初始化
        /// </summary>
        private static void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                Initialize();
            }
        }

        /// <summary>
        /// 加密数据
        /// </summary>
        /// <param name="data">要加密的数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>加密后的数据</returns>
        private static string EncryptData(string data, StorageSettings settings)
        {
            switch (settings.Encryption)
            {
                case StorageSettings.EncryptionType.AES:
                    return StorageEncryption.EncryptAES(data, settings.EncryptionPassword);
                case StorageSettings.EncryptionType.XOR:
                    return StorageEncryption.EncryptXOR(data, settings.EncryptionPassword);
                default:
                    return data;
            }
        }

        /// <summary>
        /// 解密数据
        /// </summary>
        /// <param name="encryptedData">加密的数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>解密后的数据</returns>
        private static string DecryptData(string encryptedData, StorageSettings settings)
        {
            switch (settings.Encryption)
            {
                case StorageSettings.EncryptionType.AES:
                    return StorageEncryption.DecryptAES(encryptedData, settings.EncryptionPassword);
                case StorageSettings.EncryptionType.XOR:
                    return StorageEncryption.DecryptXOR(encryptedData, settings.EncryptionPassword);
                default:
                    return encryptedData;
            }
        }

        /// <summary>
        /// 异步写入文件（带安全备份机制）
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>异步任务</returns>
        private static async Task WriteToFileAsync(string data, StorageSettings settings)
        {
            switch (settings.Location)
            {
                case StorageSettings.StorageLocation.File:
                    await WriteToFileWithBackupAsync(data, settings);
                    break;
                case StorageSettings.StorageLocation.PlayerPrefs:
                    PlayerPrefs.SetString(settings.FilePath, data);
                    PlayerPrefs.Save();
                    break;
                default:
                    throw new NotSupportedException($"Storage location {settings.Location} is not supported for writing");
            }
        }

        /// <summary>
        /// 异步写入文件，使用安全备份机制
        /// </summary>
        /// <param name="data">数据</param>
        /// <param name="settings">设置对象</param>
        /// <returns>异步任务</returns>
        private static async Task WriteToFileWithBackupAsync(string data, StorageSettings settings)
        {
            var filePath = settings.GetFullPath();
            var backupPath = settings.GetBackupPath();
            var directory = Path.GetDirectoryName(filePath);

            try
            {
                // 确保目录存在
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 先写入备份文件
                await File.WriteAllTextAsync(backupPath, data, settings.TextEncoding);

                // 如果目标文件存在，先删除
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                }

                // 将备份文件重命名为目标文件
                File.Move(backupPath, filePath);

                NLogger.Log<string>("File saved successfully: {0}", filePath);
            }
            catch (Exception ex)
            {
                // 如果保存失败，清理备份文件
                if (File.Exists(backupPath))
                {
                    try
                    {
                        File.Delete(backupPath);
                    }
                    catch
                    {
                        // 忽略删除备份文件的错误
                    }
                }

                NLogger.LogError<string>("Failed to save file: {0}", ex.Message);
                throw;
            }
        }

        /// <summary>
        /// 异步从文件读取（带文件恢复机制）
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>文件内容</returns>
        private static async Task<string> ReadFromFileAsync(StorageSettings settings)
        {
            switch (settings.Location)
            {
                case StorageSettings.StorageLocation.File:
                    return await ReadFromFileWithRecoveryAsync(settings);
                case StorageSettings.StorageLocation.PlayerPrefs:
                    return PlayerPrefs.GetString(settings.FilePath, string.Empty);
                default:
                    throw new NotSupportedException($"Storage location {settings.Location} is not supported for reading");
            }
        }

        /// <summary>
        /// 异步从文件读取，使用文件恢复机制
        /// </summary>
        /// <param name="settings">设置对象</param>
        /// <returns>文件内容</returns>
        private static async Task<string> ReadFromFileWithRecoveryAsync(StorageSettings settings)
        {
            var filePath = settings.GetFullPath();
            var backupPath = settings.GetBackupPath();

            try
            {
                // 检查文件恢复情况
                await CheckAndRecoverFileAsync(filePath, backupPath);

                // 读取主文件
                if (File.Exists(filePath))
                {
                    return await File.ReadAllTextAsync(filePath, settings.TextEncoding);
                }

                NLogger.LogWarning<string>("File does not exist: {0}", filePath);
                return string.Empty;
            }
            catch (Exception ex)
            {
                NLogger.LogError<string>("Failed to read file: {0}", ex.Message);
                return string.Empty;
            }
        }

        /// <summary>
        /// 检查并恢复文件
        /// </summary>
        /// <param name="filePath">主文件路径</param>
        /// <param name="backupPath">备份文件路径</param>
        /// <returns>异步任务</returns>
        private static async Task CheckAndRecoverFileAsync(string filePath, string backupPath)
        {
            bool mainFileExists = File.Exists(filePath);
            bool backupFileExists = File.Exists(backupPath);

            if (!mainFileExists && backupFileExists)
            {
                // 主文件不存在但备份文件存在，说明上次保存过程中发生了崩溃
                try
                {
                    File.Move(backupPath, filePath);
                    NLogger.Log<string>("Recovered file from backup: {0}", filePath);
                }
                catch (Exception ex)
                {
                    NLogger.LogError<string>("Failed to recover file from backup: {0}", ex.Message);
                }
            }
            else if (mainFileExists && backupFileExists)
            {
                // 主文件和备份文件都存在，删除备份文件
                try
                {
                    File.Delete(backupPath);
                    NLogger.Log<string>("Cleaned up backup file: {0}", backupPath);
                }
                catch (Exception ex)
                {
                    NLogger.LogWarning<string>("Failed to clean up backup file: {0}", ex.Message);
                }
            }

            await Task.CompletedTask;
        }

        #endregion

        #region 应用程序事件处理

        /// <summary>
        /// 应用程序退出时的处理
        /// </summary>
        private static void OnApplicationQuitting()
        {
            if (_isInitialized && DefaultSettings.AutoSaveOnApplicationQuit && Cache.IsDirty)
            {
                SaveToFile();
                NLogger.Log("Auto-saved data on application quit");
            }
        }

        /// <summary>
        /// 应用程序焦点变化时的处理
        /// </summary>
        /// <param name="hasFocus">是否有焦点</param>
        private static void OnApplicationFocusChanged(bool hasFocus)
        {
            if (_isInitialized && !hasFocus && DefaultSettings.AutoSaveOnApplicationPause && Cache.IsDirty)
            {
                SaveToFile();
                NLogger.Log("Auto-saved data on application pause");
            }
        }

        #endregion
    }
}
