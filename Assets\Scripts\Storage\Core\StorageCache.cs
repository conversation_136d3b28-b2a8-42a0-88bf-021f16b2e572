using System;
using System.Collections.Generic;
using UnityEngine;
using DGame.Framework;

namespace Storage
{
    /// <summary>
    /// 存储系统的内存缓存管理器，负责管理内存中的数据
    /// </summary>
    public class StorageCache
    {
        #region 私有字段

        // 使用Dictionary提供高性能访问（仅主线程使用）
        private readonly Dictionary<string, object> _cache;
        private bool _isDirty = false;

        #endregion

        #region 属性

        /// <summary>
        /// 缓存是否已被修改
        /// </summary>
        public bool IsDirty
        {
            get { return _isDirty; }
        }

        /// <summary>
        /// 缓存中的数据数量
        /// </summary>
        public int Count
        {
            get { return _cache.Count; }
        }

        /// <summary>
        /// 获取所有的键
        /// </summary>
        public ICollection<string> Keys
        {
            get { return _cache.Keys; }
        }

        #endregion

        #region 构造函数

        /// <summary>
        /// 默认构造函数
        /// </summary>
        public StorageCache()
        {
            _cache = new Dictionary<string, object>();
        }

        /// <summary>
        /// 带初始容量的构造函数
        /// </summary>
        /// <param name="initialCapacity">初始容量</param>
        public StorageCache(int initialCapacity)
        {
            _cache = new Dictionary<string, object>(initialCapacity);
        }

        #endregion

        #region 数据操作方法

        /// <summary>
        /// 设置数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="value">值</param>
        public void Set<T>(string key, T value)
        {
            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return;
            }

            _cache[key] = value;
            _isDirty = true;

            NLogger.Log("Key: {0}, Type: {1}", arg1: key, arg2: typeof(T).Name);
        }

        /// <summary>
        /// 获取数据
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <returns>数据值</returns>
        public T Get<T>(string key)
        {
            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return default(T);
            }

            if (_cache.TryGetValue(key, out object value))
            {
                try
                {
                    return (T)value;
                }
                catch (InvalidCastException)
                {
                    NLogger.LogError("Cannot cast value for key '{0}' to type {1}", arg1: key, arg2: typeof(T).Name);
                    return default(T);
                }
            }

            NLogger.LogWarning("Key '{0}' not found", arg1: key);
            return default(T);
        }

        /// <summary>
        /// 获取数据，如果不存在则返回默认值
        /// </summary>
        /// <typeparam name="T">数据类型</typeparam>
        /// <param name="key">键</param>
        /// <param name="defaultValue">默认值</param>
        /// <returns>数据值或默认值</returns>
        public T Get<T>(string key, T defaultValue)
        {
            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return defaultValue;
            }

            if (_cache.TryGetValue(key, out object value))
            {
                try
                {
                    return (T)value;
                }
                catch (InvalidCastException)
                {
                    NLogger.LogError("Cannot cast value for key '{0}' to type {1}", arg1: key, arg2: typeof(T).Name);
                    return defaultValue;
                }
            }

            return defaultValue;
        }

        /// <summary>
        /// 检查键是否存在
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否存在</returns>
        public bool ContainsKey(string key)
        {
            if (string.IsNullOrEmpty(key))
                return false;

            return _cache.ContainsKey(key);
        }

        /// <summary>
        /// 删除数据
        /// </summary>
        /// <param name="key">键</param>
        /// <returns>是否删除成功</returns>
        public bool Remove(string key)
        {
            if (string.IsNullOrEmpty(key))
            {
                NLogger.LogError("Key cannot be null or empty");
                return false;
            }

            bool removed = _cache.Remove(key);
            if (removed)
            {
                _isDirty = true;
                NLogger.Log("Key '{0}' removed successfully", arg1: key);
            }
            else
            {
                NLogger.LogWarning("Key '{0}' not found", arg1: key);
            }
            return removed;
        }

        /// <summary>
        /// 清空所有数据
        /// </summary>
        public void Clear()
        {
            int count = _cache.Count;
            _cache.Clear();
            _isDirty = true;
            NLogger.Log("Cleared {0} items from cache", arg1: count);
        }

        /// <summary>
        /// 获取所有数据的副本
        /// </summary>
        /// <returns>数据字典的副本</returns>
        public Dictionary<string, object> GetAllData()
        {
            var result = new Dictionary<string, object>();
            foreach (var kvp in _cache)
            {
                result[kvp.Key] = kvp.Value;
            }
            return result;
        }

        /// <summary>
        /// 从字典加载数据
        /// </summary>
        /// <param name="data">数据字典</param>
        public void LoadFromDictionary(Dictionary<string, object> data)
        {
            if (data == null)
            {
                NLogger.LogError("Data dictionary cannot be null");
                return;
            }

            _cache.Clear();
            foreach (var kvp in data)
            {
                _cache[kvp.Key] = kvp.Value;
            }
            _isDirty = false;
            NLogger.Log("Loaded {0} items into cache", arg1: data.Count);
        }

        /// <summary>
        /// 标记缓存为已保存状态
        /// </summary>
        public void MarkAsSaved()
        {
            _isDirty = false;
        }

        /// <summary>
        /// 强制标记缓存为脏状态
        /// </summary>
        public void MarkAsDirty()
        {
            _isDirty = true;
        }

        #endregion

        #region 调试方法

        /// <summary>
        /// 获取缓存状态信息
        /// </summary>
        /// <returns>状态信息字符串</returns>
        public string GetStatusInfo()
        {
            return $"StorageCache Status - Count: {Count}, IsDirty: {IsDirty}";
        }

        /// <summary>
        /// 打印所有缓存的键
        /// </summary>
        public void LogAllKeys()
        {
            var keys = string.Join(", ", Keys);
            NLogger.Log("All Keys: [{0}]", arg1: keys);
        }

        #endregion
    }
}
